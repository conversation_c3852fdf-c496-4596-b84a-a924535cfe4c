
### data-api
POST http://************:3000/abi/api/cloud?$.queryDataApi
Content-Type: application/json
Cookie: sugo.sess=sg.IYEwtglgdkA.HM0fFcNYUB; jwtSign=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbklkIjoiUDdZWXRKYVBJIiwidXNlcklkIjoiY29vSFVGQnNGdiIsInVzZXJuYW1l
micro-apps: sugo-abi
u-id: cooHUFBsFv
x-sing: M4Vw5g9gtAhgRgSwD4EYDsBWAzJgbGgFhQCYBONIA
x-time: 1753756741297

{
  "functionName": "queryDataApi",
  "params": {
    "isTest": true,
    "isDebug": false,
    "api": {
      "mode": "merge",
      "sign": "saleOrderHome",
      "testCode": "// 查询参数（合并时为 key-map 结构）\\n({\\n  '你的签名': {\\n    limit: 20,\\n    offset: 0,\\n    cache: 60000\\n  }\\n})\\n    ",
      "type": "themeAnalysis",
      "title": "销售订单全生命周期接口",
      "description": "销售订单全生命周期首页接口",
      "status": "active",
      "fieldColumnMap": {},
      "mainApi": {
        "id": "qrcjI0T1UV",
        "fields": [
          "syt_text1"
        ]
      },
      "subApis": [
        {
          "id": "D3q6GN2YWW",
          "fields": [
            "syt_text1"
          ]
        }
      ],
      "mergeCarryHook": null,
      "queryAfterHook": null,
      "authorizes": null,
      "createBy": "cooHUFBsFv"
    },
    "userQuery": {
      "你的签名": {
        "limit": 20,
        "offset": 0,
        "cache": 60000
      }
    }
  },
  "namespace": "$cloud",
  "modelName": "$"
}

### 明细查询
POST http://************:3000/abi/api/cloud?$.queryData
Content-Type: application/json
Cookie: sugo.sess=sg.q-HIR2CAI
micro-apps: sugo-abi

{
  "functionName": "queryData",
  "params": {
    "query": {
      "dryRun": true,
      "_title": "v_委外出库明细/默认",
      "_key": "card_4t515_tab_default",
      "themeId": "MsnIbcvnc3",
      "filters": [],
      "orderBy": [],
      "queryTotal": false,
      "roleIds": [
        "auto"
      ],
      "type": "dataCenter",
      "connId": 951,
      "dbId": 12,
      "dbName": "erp_mes_mp_test",
      "queryMode": "groupBy",
      "tableId": 225,
      "tableName": "v_dwd_delivery_outsourc_detail",
      "fieldsBinding": {
        "execute_feeding_number": {
          "id": "6091",
          "dataType": "number",
          "name": "execute_feeding_number",
          "title": "发料物料已出库总数",
          "type": "field"
        },
        "id": {
          "id": "6093",
          "dataType": "number",
          "name": "id",
          "title": "主键Id",
          "type": "field"
        },
        "line_no": {
          "id": "6096",
          "dataType": "number",
          "name": "line_no",
          "title": "委外工单明细行号",
          "type": "field"
        },
        "out_stock_quantity": {
          "id": "6078",
          "dataType": "number",
          "name": "out_stock_quantity",
          "title": "本单已出库数量",
          "type": "field"
        },
        "outsourcing_work_order_out_stock_detail_line_no": {
          "id": "6110",
          "dataType": "number",
          "name": "outsourcing_work_order_out_stock_detail_line_no",
          "title": "委外出库单明细行号",
          "type": "field"
        },
        "plan_feeding_number": {
          "id": "6081",
          "dataType": "number",
          "name": "plan_feeding_number",
          "title": "发料物料需求总数",
          "type": "field"
        },
        "plan_out_stock_quantity": {
          "id": "6084",
          "dataType": "number",
          "name": "plan_out_stock_quantity",
          "title": "本单计划出库数量",
          "type": "field"
        },
        "single_machine_quantity": {
          "id": "6076",
          "dataType": "number",
          "name": "single_machine_quantity",
          "title": "单机数量",
          "type": "field"
        },
        "state": {
          "id": "6092",
          "dataType": "number",
          "name": "state",
          "title": "状态",
          "type": "field"
        },
        "outsourcingProgress": {
          "id": "6088",
          "dataType": "number",
          "name": "outsourcingProgress",
          "title": "委外出库进度",
          "type": "field"
        },
        "must_qty": {
          "id": "6095",
          "dataType": "number",
          "name": "must_qty",
          "title": "计划发料数量",
          "type": "field"
        },
        "picked_qty": {
          "id": "6104",
          "dataType": "number",
          "name": "picked_qty",
          "title": "已执行发料数量",
          "type": "field"
        },
        "create_user_name": {
          "id": "6077",
          "dataType": "string",
          "name": "create_user_name",
          "title": "创建人",
          "type": "field"
        },
        "last_modification_time": {
          "id": "6107",
          "dataType": "date",
          "name": "last_modification_time",
          "title": "最后修改时间",
          "type": "field"
        },
        "material_code": {
          "id": "6108",
          "dataType": "string",
          "name": "material_code",
          "title": "物料代码",
          "type": "field"
        },
        "material_model": {
          "id": "6080",
          "dataType": "string",
          "name": "material_model",
          "title": "物料型号",
          "type": "field"
        },
        "material_name": {
          "id": "6094",
          "dataType": "string",
          "name": "material_name",
          "title": "物料名称",
          "type": "field"
        },
        "material_specification": {
          "id": "6082",
          "dataType": "string",
          "name": "material_specification",
          "title": "物料规格",
          "type": "field"
        },
        "out_stock_user_name": {
          "id": "6098",
          "dataType": "string",
          "name": "out_stock_user_name",
          "title": "出库人",
          "type": "field"
        },
        "outsourcing_order_code": {
          "id": "6087",
          "dataType": "string",
          "name": "outsourcing_order_code",
          "title": "委外订单号",
          "type": "field"
        },
        "outsourcing_work_order_code": {
          "id": "6083",
          "dataType": "string",
          "name": "outsourcing_work_order_code",
          "title": "委外工单号",
          "type": "field"
        },
        "outsourcing_work_order_out_stock_no": {
          "id": "6101",
          "dataType": "string",
          "name": "outsourcing_work_order_out_stock_no",
          "title": "委外出库单号",
          "type": "field"
        },
        "receipt_type_code": {
          "id": "6090",
          "dataType": "string",
          "name": "receipt_type_code",
          "title": "单据类型代码",
          "type": "field"
        },
        "receipt_type_name": {
          "id": "6109",
          "dataType": "string",
          "name": "receipt_type_name",
          "title": "单据类型名称",
          "type": "field"
        },
        "remarks": {
          "id": "6105",
          "dataType": "string",
          "name": "remarks",
          "title": "备注",
          "type": "field"
        },
        "supplier_id": {
          "id": "6103",
          "dataType": "string",
          "name": "supplier_id",
          "title": "供应商",
          "type": "field"
        },
        "unit_code": {
          "id": "6089",
          "dataType": "string",
          "name": "unit_code",
          "title": "单位代码",
          "type": "field"
        },
        "unit_name": {
          "id": "6099",
          "dataType": "string",
          "name": "unit_name",
          "title": "单位名称",
          "type": "field"
        },
        "update_user_name": {
          "id": "6097",
          "dataType": "string",
          "name": "update_user_name",
          "title": "更新人",
          "type": "field"
        },
        "warehouse_code": {
          "id": "6079",
          "dataType": "string",
          "name": "warehouse_code",
          "title": "仓库代码",
          "type": "field"
        },
        "warehouse_name": {
          "id": "6100",
          "dataType": "string",
          "name": "warehouse_name",
          "title": "仓库名称",
          "type": "field"
        },
        "creation_time": {
          "id": "6075",
          "dataType": "date",
          "name": "creation_time",
          "title": "创建时间",
          "type": "field"
        },
        "executeTime": {
          "id": "6106",
          "dataType": "date",
          "name": "executeTime",
          "title": "执行时间",
          "type": "field"
        }
      },
      "limit": 20,
      "offset": 0,
      "staleAfter": 21600,
      "timeBucket": "",
      "cacheRefresh": true
    }
  },
  "namespace": "$cloud",
  "modelName": "$"
}

### 明细查询2
POST http://************:3000/abi/api/cloud?$.queryData
content-type: application/json;charset=UTF-8
micro-apps: sugo-abi
Cookie: sugo.sess=sg.nUKNRsrlK
micro-apps: sugo-abi
u-id: cooHUFBsFv
x-sing: M4Vw5g9gtAhgllAtjAdjMBTRGUBcA+AjAOwCsAzMcYQAyUCctQA
x-time: 1753771037910

{
  "functionName": "queryData",
  "params": {
    "query": {
      "dryRun": false,
      "_title": "未报工数量--生产订单/默认",
      "_key": "card_ect4n_tab_default",
      "themeId": "cooHUFBsFv_new",
      "filters": [
        {
          "key": "gcnq7",
          "op": "equal",
          "col": "production_order_no",
          "eq": "MO0008062",
          "selected": "9OgwxujP0j",
          "dataType": "string",
          "colName": "生产任务单号"
        }
      ],
      "orderBy": [
        {
          "field": "time_date",
          "dir": "asc"
        }
      ],
      "queryTotal": false,
      "roleIds": [
        "auto"
      ],
      "type": "indicesTable",
      "queryMode": "groupBy",
      "tableName": "index_data_20250706",
      "fieldsBinding": {
        "wbgsl11scdd": {
          "id": "VLsPg04tl:default_spec",
          "dataType": "number",
          "name": "_tempMetric_wbgsl11scdd",
          "title": "未报工数量--生产订单",
          "type": "indicesSpec"
        },
        "production_order_no": {
          "id": "9OgwxujP0j",
          "dataType": "string",
          "name": "production_order_no",
          "title": "生产任务单号",
          "type": "indicesDims"
        },
        "time_date": {
          "id": "time_date",
          "dataType": "date",
          "name": "time_date",
          "title": "统计时间",
          "type": "indicesDims"
        }
      },
      "limit": 100,
      "offset": 0,
      "staleAfter": 0,
      "timeBucket": "DAY",
      "cacheRefresh": true
    }
  },
  "namespace": "$cloud",
  "modelName": "$"
}
