import { IMiddleware } from '@midwayjs/core'
import { Inject, Middleware } from '@midwayjs/decorator'
import { Context, NextFunction } from '@midwayjs/koa'
import { RedisService } from '@midwayjs/redis'
import * as cookieKit from 'cookie'
import _ from 'lodash'

import {
  COOKIE_KEY,
  GET_DATASET_COLUMNS_FUNC,
  PERMIMSSION_MODELS,
  PERMISSION_QUERY_TYPE,
  QUERY_DATA_FUNC
} from '@/consts'
import type { RedisInfo } from '@/types/permission'
import { getPermissionWhere } from '@/utils'

@Middleware()
export class PermissionMiddleware implements IMiddleware<Context, NextFunction> {
  @Inject()
  redis: RedisService

  private getPermissionDataFromRedis = async (cookie: string) => {
    const cookieObj = cookieKit.parse(cookie)
    const sessionId = _.get(cookieObj, COOKIE_KEY) || _.get(cookieObj, encodeURIComponent(COOKIE_KEY))
    // 因为主应用保存用户信息的redis使用的是数据库0，因此redis要选择0数据库并通过cookie传入的值去获取用户的信息及权限信息，使用pipeline方法的原因是此操作选择数据库后不影响原来配置的redis数据库
    const redisRes = await this.redis.pipeline().select(0).get(sessionId).exec()
    const permisstionStr = _.get(redisRes, [1, 1], '') as string
    const redisObj: RedisInfo = permisstionStr ? JSON.parse(permisstionStr as string) : {}
    return redisObj
  }

  /** 判断当前接口是否需要权限控制 */
  private checkNeedPermission = (functionName: string, modelName: string, params: any) => {
    const isPermissionFunc = _.startsWith(functionName, 'find') || ['groupBy', 'aggregate'].includes(functionName)

    const type = _.get(params, ['query', 'type'])

    // queryData方法 只针对数据集及数据库进行权限控制
    const isQueryPermission = PERMISSION_QUERY_TYPE.includes(type) && functionName === QUERY_DATA_FUNC

    // 只针对数据模型相关模块的find方法进行权限控制
    const isModelPermission = (PERMIMSSION_MODELS.includes(_.toLower(modelName)) && isPermissionFunc)
      || GET_DATASET_COLUMNS_FUNC === functionName
    return isModelPermission || isQueryPermission
  }

  resolve() {

    return async (ctx: Context, next: NextFunction) => {

      // 如果禁用权限，直接返回
      if (process.env?.DISABLED_PERMISSION === 'on') {
        return next()
      }

      const { functionName, modelName, params } = (ctx.request.body || {}) as any

      const isNeedPermission = this.checkNeedPermission(functionName, modelName, params)

      // 判断要注入权限的模块及方法
      if (isNeedPermission) {
        try {
          const cookie = ctx.headers.cookie as string

          if (_.isEmpty(cookie)) {
            ctx.status = 401
            return ctx.body = {
              success: false,
              message: '请先登录',
              result: []
            }
          }

          const { dataPermission, user } = await this.getPermissionDataFromRedis(cookie)

          if (_.isEmpty(user)) {
            ctx.status = 401
            return ctx.body = {
              success: false,
              message: '请先登录',
              result: []
            }
          }

          // 管理员不进行权限控制
          if (user?.type === 'built-in') return next()

          if (_.isEmpty(dataPermission)) {
            ctx.status = 401
            return ctx.body = {
              success: false,
              message: '当前用户无权限，获取权限数据失败',
              result: []
            }
          }

          if (functionName === QUERY_DATA_FUNC) {
            _.set(ctx.request.body as any, 'params', { ...(params || {}), dataPermission })
          } else {
            params.where = getPermissionWhere({ modelName, functionName, params, dataPermission, user })
            ctx.logger.info(`添加权限后的params=${JSON.stringify(params)}`)
            _.set(ctx.request.body as any, 'params', params)
          }

        } catch (error) {
          ctx.logger.error(`注入权限失败, 原因为： ${error?.message}, stack: ${JSON.stringify(error?.stack)}`)
          ctx.status = 401
          return ctx.body = {
            success: false,
            message: '当前用户无权限，注入权限失败',
            result: []
          }
        }
      }
      await next()
    }
  }

  static getName(): string {
    return 'permission'
  }
}
