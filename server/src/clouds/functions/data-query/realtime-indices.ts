import { mapAwaitAll } from '@utils/util'
import _ from 'lodash'
import { RequestMethod } from 'umi-request'

import { CheckDeriveOtherIndiceInfoPatched, fetchSql, getSafeFilters } from '@/clouds/functions/data-query/query-helper/data-loader'
import { DEFAULT_LIMIT, isAggCol } from '@/clouds/functions/data-query/query-helper/sql-builder'
import { ColumnInfo, DataSourceQueryConfig } from '@/types/data-source'
import { IndicesBase } from '@/types/indices-table'

/**
 * 获取复合指标的计算公式
 * @param mInf
 * @param verNames
 * @param withTableName
 */
export function getCompositeFormulaWithCode(mInf: IndicesBase, verNames?: string[], withTableName = true) {
  verNames = _.compact(verNames)
  const vers = mInf.indicesBaseVersion
  const targetVer =
    _.isEmpty(verNames) || _.includes(verNames, '*')
      ? _.maxBy(_.filter(vers, { status: 1 }), 'createdAt')
      : _.find(vers, v => v.name === verNames[0])
  const vp = mInf.baseFilters.versionParams
  const { formula, indices } = (vp?.find(p => p.versionId === targetVer?.id) || vp?.[0])?.params || {}
  // {
  //     "formula": " / ",
  //     "indices": [
  //         { "id": "ZOaHq-7hJ", "index": 0, "enName": "b00026", "filters": [], "versionId": "ur1VnZfloG" },
  //         { "id": "r31R-S8y3", "index": 3, "enName": "b00048", "filters": [], "versionId": "SRlLSVvriq" }
  //     ],
  //     "parameters": []
  // }
  return _.orderBy(indices, 'index', 'desc').reduce((acc, o, i) => {
    // 往 acc 的 index 插入 enName
    const { enName, index } = o
    const colName = withTableName ? `${enName}_data.${enName}` : enName
    return acc.slice(0, index) + colName + acc.slice(index)
  }, formula)
}

/** 查询实时指标 */
export async function queryRealtimeIndicesTable(
  queryConfig: DataSourceQueryConfig,
  req: Record<string, RequestMethod>,
  infos: CheckDeriveOtherIndiceInfoPatched[]
) {
  const { fieldsBinding, filters, timeBucket, orderBy, limit = DEFAULT_LIMIT, staleAfter } = queryConfig
  const [dims, metrics] = _.partition(fieldsBinding, c => !isAggCol(c))
  const recurFlat = (arr: any) =>
    _.flatMap(arr, c => (_.isEmpty(c.composedMetricInfos) ? c : [c, ...recurFlat(c.composedMetricInfos)]))
  const infoDict = _.keyBy(recurFlat(infos), o => o.baseIndiceId.split(':')[0])
  const safeFilters = getSafeFilters(filters, timeBucket)
  const sCache = staleAfter ?? 'PT4H'

  const genSqlParams = {
    indices: _.flatMap(metrics, m => {
      const { id, versions: verNames } = m
      const [baseIndiceId, caliberId] = id.split(':')
      const extraInfo = infoDict[baseIndiceId]
      const mInf = extraInfo?.baseInfo
      if (!_.some(extraInfo.composedMetricInfos)) {
        // 原子实时指标
        const vers = mInf.indicesBaseVersion
        const targetVer =
          _.isEmpty(verNames) || _.includes(verNames, '*')
            ? _.maxBy(_.filter(vers, { status: 1 }), 'createdAt')
            : _.find(vers, v => v.name === verNames[0])
        return { indiceId: baseIndiceId, caliberId, versionId: targetVer?.id || '', _filters: [] }
      }
      // 复合实时指标，查询全部子指标的 sql
      const vers = mInf.indicesBaseVersion
      const targetVer =
        _.isEmpty(verNames) || _.includes(verNames, '*')
          ? _.maxBy(_.filter(vers, { status: 1 }), 'createdAt')
          : _.find(vers, v => v.name === verNames[0])

      const vp = mInf.baseFilters.versionParams
      const { indices: subIndices } = (vp?.find(p => p.versionId === targetVer?.id) || vp?.[0])?.params || {}
      // {
      //     "formula": " / ",
      //     "indices": [
      //         { "id": "ZOaHq-7hJ", "index": 0, "enName": "b00026", "filters": [], "versionId": "ur1VnZfloG" },
      //         { "id": "r31R-S8y3", "index": 3, "enName": "b00048", "filters": [], "versionId": "SRlLSVvriq" }
      //     ],
      //     "parameters": []
      // }
      const subIndicesDict = _.keyBy(subIndices, 'id')

      return _.flatMap(extraInfo.composedMetricInfos, c => {
        const [subMId, specId] = c.baseIndiceId.split(':')
        const { filters: subMetFlts, versionId } = subIndicesDict[subMId] || {}
        const subMetVers = c?.baseInfo.indicesBaseVersion
        const lv =
          _.find(subMetVers, v => v.id === versionId) || _.maxBy(_.filter(subMetVers, { status: 1 }), 'createdAt')
        return {
          indiceId: subMId,
          caliberId: lv?.specId || specId || 'default_spec',
          versionId: lv?.id || '',
          _filters: subMetFlts
        }
      })
    }),
    dimensionIds: _.map(dims, 'id'),
    granularity: [timeBucket],
    filters: safeFilters,
    orderBy: _.map(orderBy, o => ({
      col: fieldsBinding[o.field]?.name?.replace('_tempMetric_', '') || o.field || o.col,
      dir: o.dir
    })),
    limit,
    query: false
  }

  // console.log('realtimeIndicesQuery:', JSON.stringify(realtimeIndicesQuery, null, 2))
  const sqlDict = {}
  // 如果子指标带有筛选条件，则需要另外查询
  const [indicesWithFilters, indicesWithoutFilters] = _.partition(genSqlParams.indices, o => _.some(o._filters))
  const buildSqlServiceUrl = `/app/indices-derive/online-indices-query?sCache=${sCache}`
  const indicesWithFiltersRes = await mapAwaitAll(indicesWithFilters, async o => {
    const res = await req.totalMutRequester.post(buildSqlServiceUrl, {
      data: {
        ...genSqlParams,
        indices: [_.omit(o, '_filters')],
        filters: [...genSqlParams.filters, ...o._filters]
      }
    })
    return res?.result || {}
  })
  const indicesWithoutFiltersRes = await req.totalMutRequester.post(buildSqlServiceUrl, {
    data: { ...genSqlParams, indices: indicesWithoutFilters }
  })
  Object.assign(sqlDict, indicesWithoutFiltersRes?.result || {}, ...indicesWithFiltersRes)
  // {
  //     "r31R-S8y3": {
  //         "DAY": {
  //             "sql": "SELECT substring(date_format(table_do0kx88hg.`date`, '%Y%m%d') , 1, 8) as `time_date`,
  //             CAST(SUM(`table_do0kx88hg`.`volume`) as DECIMAL(20, 4)) as `b00048`
  //             FROM indices_data_set.table_do0kx88hg table_do0kx88hg WHERE (1=1)
  //             GROUP BY substring(date_format(table_do0kx88hg.`date`, '%Y%m%d') , 1, 8)",
  //             "dbId": "1095"
  //         }
  //     }
  // }
  // 合并复合指标的 sql，通过构造 join 来创建新的 sql
  const sqls = _.map(metrics, m => {
    const { id, versions: verNames } = m
    const [mId, caliberId] = id.split(':')
    const extraInfo = infoDict[mId]
    const mInf = extraInfo?.baseInfo
    // 修正指标名，使得后续的改名逻辑有效
    m.name = mInf.code
    if (!_.some(extraInfo.composedMetricInfos)) {
      const q = sqlDict[mId]?.[timeBucket]
      return {
        sql: q?.sql,
        dbId: q?.dbId,
        dims: _.map(dims, d => d.name),
        metrics: [mInf.code]
      }
    }
    // 复合指标，需要合并子指标 sql
    const formulaWithEnName = getCompositeFormulaWithCode(mInf, verNames)

    const subMetricsSql = _.map(extraInfo.composedMetricInfos, o => {
      const sm = o?.baseInfo
      const tableAlias = `${sm.code}_data`
      const q = sqlDict[sm.id]?.[timeBucket]
      return {
        sql: q?.sql,
        dbId: q?.dbId,
        tableAlias,
        dims: _.map(dims, d => `${tableAlias}.${d.name}`),
        metrics: [`${tableAlias}.${mInf.code}`]
      }
    })

    const fromSrc = subMetricsSql[0]
    const selects = [..._.map(fromSrc.dims, dn => dn), `${formulaWithEnName} as ${mInf.code}`].join(', ')
    const joins = _.drop(subMetricsSql, 1).map(d => {
      const joinOn = _.map(d.dims, (dn, idx) => `${dn} = ${fromSrc.dims[idx]}`).join(' AND ')
      return `LEFT JOIN (${d.sql}) ${d.tableAlias} ON ${joinOn}`
    })
    return {
      sql: `SELECT ${selects} FROM (${fromSrc.sql}) ${fromSrc.tableAlias} ${joins.join(' ')}`,
      dbId: fromSrc.dbId,
      dims: _.map(dims, 'name'),
      metrics: [mInf.code]
    }
  })
  if (queryConfig.dryRun) {
    return [{ __msg: { genSqlParams, sqlDict, finalSqls: sqls }, __code: 0, __data: null }]
  }

  // 查询数据，然后 join 合并多个指标的结果
  const results = await mapAwaitAll(sqls, async o => {
    const { sql, dbId, metrics: metricNames } = o
    const queryRes = await fetchSql(req.masterRequester, { dbId, sql, isReturnFields: 0, maxRow: limit, sCache })
    const { code, data, success, message } = queryRes || {}
    // 对于 metrics，需要转数值
    const metricNameSet = new Set(metricNames)
    return _.map(data?.data, d => _.mapValues(d, (v, k) => (metricNameSet.has(k) ? +v : v)))
  })

  // 合并逻辑放外面了，实际上每次只会查询一个指标
  return results[0]
}

/** 计算复杂的复合指标，例如实时复合指标里面含有实时复合或实时与非实时混合，需要递归查询 */
export async function resolveComplexColumn(
  info: CheckDeriveOtherIndiceInfoPatched,
  doQuery: (queryMod: (queryConfig: DataSourceQueryConfig) => DataSourceQueryConfig) => Promise<Record<string, any>[]>
) {
  // 1. 获取当前指标的计算公式，构造求值函数
  const { baseInfo, composedMetricInfos, versionName } = info
  const formulaWithEnName = getCompositeFormulaWithCode(baseInfo, [versionName], false)
  // 适配指标值为 undefined 的情况
  const usedColsDict = {}
  const safeFormula = formulaWithEnName.replace(/\b[a-zA-Z_]\w+\b/g, m => {
    usedColsDict[m] = m
    return `(${m} || 0)`
  })
  const evalFn = d => {
    const safeData = _.mapValues(usedColsDict, (v, k) => d[k] ?? 0)
    return _.template(`<%= ${safeFormula} %>`)(safeData)
  }
  // 2. 递归获取所有子指标的 join 结果
  const newCols = _.zipObject(
    _.map(composedMetricInfos, c => c.baseIndicesCode),
    _.map(
      composedMetricInfos,
      c =>
      ({
        id: c.baseIndiceId,
        name: c.baseIndicesCode,
        type: 'indicesSpec',
        dataType: 'number'
      } as ColumnInfo)
    )
  )
  let newQuery = null
  const subColRes = await doQuery(queryConfig => {
    newQuery = {
      ...queryConfig,
      fieldsBinding: { ..._.omitBy(queryConfig.fieldsBinding, c => isAggCol(c)), ...newCols }
    }
    return newQuery
  })
  // 3. 通过计算公式计算当前指标的值
  if (newQuery.dryRun) {
    return [
      {
        ...(subColRes[0] || {}),
        [`_complexQuery_${info.baseIndiceId}`]: newQuery,
        [`_complexFormula_${info.baseIndiceId}`]: safeFormula
      }
    ]
  }
  const dimColNames = _.map(
    _.filter(newQuery.fieldsBinding, c => !isAggCol(c)),
    'name'
  )
  return _.map(subColRes, row => ({ ..._.pick(row, dimColNames), [baseInfo.code]: +evalFn(row) }))
}
