/**
 * 测试纯实时复合指标修复效果
 */

// 模拟用户的复合指标数据（基于真实数据结构）
const mockCompositeInfo = {
  baseIndiceId: 'VLsPg04tl:default_spec',
  sqlModelId: 'complex_VLsPg04tl:default_spec',
  composedMetricInfos: [
    {
      baseIndiceId: 'P3-LH0WVH:default_spec',
      baseIndicesCode: 'dbgslzh11jhwgsl11scdd',
      sqlModelId: 'complex_P3-LH0WVH:default_spec',
      composedMetricInfos: undefined,
      baseInfo: {
        id: 'P3-LH0WVH',
        type: 'base',
        isLanding: false
      }
    },
    {
      baseIndiceId: 'uRZ5mdpqO:default_spec',
      baseIndicesCode: 'ljbgsl11wgsl11scdd',
      sqlModelId: 'complex_uRZ5mdpqO:default_spec',
      composedMetricInfos: undefined,
      baseInfo: {
        id: 'uRZ5mdpqO',
        type: 'base',
        isLanding: false
      }
    }
  ]
}

// 模拟所有指标信息
const mockAllIndicesInfos = [
  {
    baseIndiceId: 'VLsPg04tl:default_spec',
    sqlModelId: 'complex_VLsPg04tl:default_spec',
    composedMetricInfos: [/* ... */]
  },
  {
    baseIndiceId: 'P3-LH0WVH:default_spec',
    sqlModelId: 'realtime',
    composedMetricInfos: []
  },
  {
    baseIndiceId: 'uRZ5mdpqO:default_spec',
    sqlModelId: 'realtime',
    composedMetricInfos: []
  }
]

// 复制修复后的检测函数逻辑
function isPureRealtimeComposite(compositeInfo, allIndicesInfos) {
  console.log('检测纯实时复合指标:', {
    baseIndiceId: compositeInfo?.baseIndiceId,
    hasComposedMetrics: !!compositeInfo?.composedMetricInfos?.length,
    composedMetricsCount: compositeInfo?.composedMetricInfos?.length || 0
  })

  if (!compositeInfo?.composedMetricInfos?.length) {
    console.log('检测失败：没有子指标信息')
    return false
  }

  console.log('子指标详细检查:')
  const results = compositeInfo.composedMetricInfos.map(subMetric => {
    // 检查子指标的 baseInfo 来判断是否为实时指标
    const baseInfo = subMetric.baseInfo

    const isRealtimeMetric = baseInfo && (
      baseInfo.isLanding === false ||  // 实时指标的关键特征
      baseInfo.type === 'base'         // 基础指标类型
    )

    // 检查是否为嵌套复合指标（真正的复合指标会有 composedMetricInfos）
    const isNestedComposite = subMetric.composedMetricInfos && subMetric.composedMetricInfos.length > 0

    const result = {
      subMetricId: subMetric.baseIndiceId,
      sqlModelId: subMetric.sqlModelId,
      baseInfoType: baseInfo?.type,
      isLanding: baseInfo?.isLanding,
      isRealtimeMetric,
      isNestedComposite,
      isValidRealtimeMetric: isRealtimeMetric && !isNestedComposite
    }

    console.log(`- ${subMetric.baseIndiceId}:`, result)
    return result.isValidRealtimeMetric
  })

  const isPure = results.every(r => r)
  console.log('最终检测结果:', isPure ? '✅ 纯实时复合指标' : '❌ 非纯实时复合指标')

  return isPure
}

// 测试函数
function testPureRealtimeDetection() {
  console.log('=== 测试纯实时复合指标检测 ===')

  const result = isPureRealtimeComposite(mockCompositeInfo, mockAllIndicesInfos)

  console.log('输入复合指标:', {
    baseIndiceId: mockCompositeInfo.baseIndiceId,
    sqlModelId: mockCompositeInfo.sqlModelId,
    subMetricsCount: mockCompositeInfo.composedMetricInfos.length,
    subMetrics: mockCompositeInfo.composedMetricInfos.map(sub => sub.baseIndiceId)
  })

  console.log('子指标检查结果:')
  mockCompositeInfo.composedMetricInfos.forEach(subMetric => {
    const subInfo = mockAllIndicesInfos.find(info =>
      info.baseIndiceId.split(':')[0] === subMetric.baseIndiceId.split(':')[0]
    )
    console.log(`- ${subMetric.baseIndiceId}:`, {
      found: !!subInfo,
      sqlModelId: subInfo?.sqlModelId,
      isRealtime: subInfo?.sqlModelId === 'realtime',
      hasComposedMetrics: !!subInfo?.composedMetricInfos?.length,
      isValidRealtimeMetric: subInfo?.sqlModelId === 'realtime' && !subInfo.composedMetricInfos?.length
    })
  })

  console.log('\n检测结果:', result ? '✅ 纯实时复合指标' : '❌ 非纯实时复合指标')

  return result
}

// 测试混合场景
function testMixedScenario() {
  console.log('\n=== 测试混合场景检测 ===')

  const mixedCompositeInfo = {
    baseIndiceId: 'mixed:default_spec',
    sqlModelId: 'complex_mixed:default_spec',
    composedMetricInfos: [
      {
        baseIndiceId: 'P3-LH0WVH:default_spec',
        baseIndicesCode: 'realtime_metric'
      },
      {
        baseIndiceId: 'warehouse_metric:default_spec',
        baseIndicesCode: 'warehouse_metric'
      }
    ]
  }

  const mixedAllIndicesInfos = [
    ...mockAllIndicesInfos,
    {
      baseIndiceId: 'warehouse_metric:default_spec',
      sqlModelId: 'warehouse_model',
      composedMetricInfos: []
    }
  ]

  const result = isPureRealtimeComposite(mixedCompositeInfo, mixedAllIndicesInfos)

  console.log('混合场景检测结果:', result ? '❌ 错误识别为纯实时' : '✅ 正确识别为混合场景')

  return !result // 混合场景应该返回false
}

// 运行测试
function runTests() {
  console.log('🚀 开始测试纯实时复合指标检测逻辑\n')

  const test1 = testPureRealtimeDetection()
  const test2 = testMixedScenario()

  console.log('\n📊 测试总结:')
  console.log('纯实时场景检测:', test1 ? '✅ 通过' : '❌ 失败')
  console.log('混合场景检测:', test2 ? '✅ 通过' : '❌ 失败')

  const allPassed = test1 && test2
  console.log('\n🎯 总体结果:', allPassed ? '✅ 所有测试通过' : '❌ 存在失败的测试')

  if (allPassed) {
    console.log('\n🎉 修复逻辑验证成功！')
    console.log('- 纯实时复合指标将使用优化处理逻辑')
    console.log('- 混合场景将继续使用原有逻辑')
    console.log('- 预期查询次数从3次减少到1次')
  }

  return allPassed
}

// 如果直接运行此脚本
if (require.main === module) {
  runTests()
}

module.exports = {
  isPureRealtimeComposite,
  testPureRealtimeDetection,
  testMixedScenario,
  runTests
}
