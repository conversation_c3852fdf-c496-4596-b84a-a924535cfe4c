import { flatMapAwaitAll, mapAwaitAll } from '@utils/util'
import { from } from 'arquero'
import _ from 'lodash'
import { RequestMethod } from 'umi-request'

import {
  CheckDeriveOtherIndiceInfoPatched,
  getConnByDbId,
  loadIndicesExtraInfo,
  patchIndicesInfos,
  patchPlanData
} from '@/clouds/functions/data-query/query-helper/data-loader'
import { resolveCompareSpec } from '@/clouds/functions/data-query/query-helper/data-process'
import { adaptDateStringFilter, isAggCol } from '@/clouds/functions/data-query/query-helper/sql-builder'
import { queryRealtimeIndicesTable, resolveComplexColumn } from '@/clouds/functions/data-query/realtime-indices'
import { querySqlMappingModel } from '@/clouds/functions/data-query/sql-mapping-model'
import { queryIndicatorData } from '@/clouds/functions/data-query/tindex'
import { ColumnInfo, DataSourceQueryConfig } from '@/types/data-source'

/**
 * 检测是否为纯实时复合指标
 * @param compositeInfo 复合指标信息
 * @returns 是否为纯实时复合指标
 */
function isPureRealtimeComposite(compositeInfo: any): boolean {
  if (!compositeInfo?.composedMetricInfos?.length) {
    return false
  }

  // 检查所有子指标是否都是实时指标
  return compositeInfo.composedMetricInfos.every((subMetric: any) => {
    const baseInfo = subMetric.baseInfo

    // 实时指标特征：isLanding === false 且 type === 'base'
    const isRealtimeMetric = baseInfo &&
      baseInfo.isLanding === false &&
      baseInfo.type === 'base'

    // 不是嵌套复合指标
    const isNotNestedComposite = !subMetric.composedMetricInfos || subMetric.composedMetricInfos.length === 0

    return isRealtimeMetric && isNotNestedComposite
  })
}

/**
 * 数仓指标查时点值，需要将 aggMode 变为 last
 * 另外需要注意带有合计值的维度，文档参考 CheckDeriveOtherIndiceInfoPatched.cumulativeDimensionValueDict
 * @param queryConfig
 * @param infos
 */
function patchFieldsByTimePointValueConfig(
  queryConfig: DataSourceQueryConfig,
  infos: CheckDeriveOtherIndiceInfoPatched[]
): DataSourceQueryConfig {
  const mergedCumulativeDimValueDict = _.assign(
    {},
    ..._.map(infos, inf => _.omitBy(inf.cumulativeDimensionValueDict, _.isEmpty))
  )
  const metricInfoIdDict = _.keyBy(infos, 'baseIndiceId')
  if (!_.some(infos, inf => inf.valueType === 'timePointerValue') && _.isEmpty(mergedCumulativeDimValueDict)) {
    return queryConfig
  }
  const { fieldsBinding } = queryConfig
  const usingDims = _.filter(fieldsBinding, c => !isAggCol(c))
  const dimNameDict = _.keyBy(usingDims, d => _.toLower(d.name))

  return {
    ...queryConfig,
    _cumulativeDimension: _.first(_.keys(mergedCumulativeDimValueDict)),
    fieldsBinding: _.mapValues(queryConfig.fieldsBinding, c => {
      if (!isAggCol(c)) {
        return c
      }
      const metricInfo = metricInfoIdDict[c.id]
      if (metricInfo?.valueType !== 'timePointerValue') {
        // 对于 非时点值，查询分析维度 包含和不包含 cumulativeDimension 维度的条件都是 cumulativeDimension != cumulativeDimensionValue
        return !_.some(mergedCumulativeDimValueDict)
          ? c
          : ({
            ...c,
            _filters: _.map(mergedCumulativeDimValueDict, (v, k) => ({ col: k, op: 'not equal', eq: [v] }))
          } as ColumnInfo)
      }
      return {
        ...c,
        // 查询 时点值时，如果 aggMode 为默认，变为 last
        aggMode: /sum|unknown/i.test(c.aggMode || 'unknown') ? 'last' : c.aggMode,
        // 查询 时点值 时，当维度分组不包含此维度，查筛选此维度 sqlColumnName = '合计' 的值；如果包含，就排除这一行
        _filters: !_.some(mergedCumulativeDimValueDict)
          ? []
          : _.map(mergedCumulativeDimValueDict, (v, k) => ({
            col: k,
            op: dimNameDict[_.toLower(k)] ? 'not equal' : 'equal',
            eq: [v]
          }))
      } as ColumnInfo
    })
  } as DataSourceQueryConfig
}

/**
 * 混合指标查询
 * 将不同类型的指标分开查询，然后通过 fullJoin 合并
 */
export async function queryAnyIndices(queryConfig: DataSourceQueryConfig, req: Record<string, RequestMethod>, db) {
  const requester = req.masterRequester
  // 查询数仓指标前，需要先查到数仓指标模型的信息，如 sql，高表列名等，参考类型 CheckDeriveOtherIndiceInfoPatched
  const otherIndiceInfos = await loadIndicesExtraInfo(queryConfig, req.totalMutRequester)
  const patchedIndicesInfos = await patchIndicesInfos({
    otherIndiceInfos,
    requester: req.totalMutRequester,
    timeBucket: queryConfig.timeBucket,
    sCache: queryConfig.staleAfter ?? 'PT4H'
  })
  // 将不同类型的指标分开查询，如数仓指标，不同模型要分开查询，mindex 指标单独查询，实时指标每个都单独查询，之后再进行 fullJoin 合并
  const queryGroup = _.groupBy(patchedIndicesInfos, inf =>
    inf?.sqlModelId === 'realtime' ? `realtime_${inf.baseIndiceId}` : inf?.sqlModelId || 'mindex'
  )
  const doQuery = async (infos: CheckDeriveOtherIndiceInfoPatched[], gId: string) => {
    const sqlModelId = `${gId}`
    // 筛选 fieldsBinding 中的指标列
    const patchedQueryConfig = patchFieldsByTimePointValueConfig(queryConfig, infos)
    const subQueryConfig: DataSourceQueryConfig = {
      ...patchedQueryConfig,
      fieldsBinding: _.pickBy(
        patchedQueryConfig.fieldsBinding,
        c => c?.type !== 'indicesSpec' || _.some(infos, inf => _.startsWith(inf.baseIndiceId, c.id))
      )
    }

    // 实时指标查询（单指标、复合）
    if (/^realtime/i.test(sqlModelId) || /^complex_/i.test(sqlModelId)) {
      // 实时指标
      const needCalcCompareSpecs = _.filter(subQueryConfig.fieldsBinding, c => isAggCol(c))
        .map(c => c.name)
      const subQueryConfigAdaptYoy = adaptDateStringFilter({
        // 由于查实时指标目前不支持 limit 和时间排序，所以 limit 不能查太小，以便后续排序？ TODO 什么场景
        // queryConfig: { ...subQueryConfig, limit: subQueryConfig.obey ? subQueryConfig.limit : Math.max(subQueryConfig.limit, 1000) },
        queryConfig: subQueryConfig, // 去掉默认 1000 limit 不然接口和AI会不准
        needCalcCompareSpecs
      })
      // 复杂的复合指标，例如实时复合指标里面含有实时复合或实时与非实时混合，需要递归查询
      let resultSet
      if (/^complex_/i.test(sqlModelId)) {
        // 检查是否为纯实时复合指标
        if (isPureRealtimeComposite(infos?.[0])) {
          // 纯实时复合指标：直接使用 queryRealtimeIndicesTable 统一处理
          resultSet = await queryRealtimeIndicesTable(subQueryConfigAdaptYoy, req, infos)
        } else {
          // 混合或嵌套复合指标：使用原始的 resolveComplexColumn 逻辑
          resultSet = await resolveComplexColumn(infos?.[0], fn => queryAnyIndices(fn(subQueryConfigAdaptYoy), req, db))
        }
      } else {
        // 普通实时指标
        resultSet = await queryRealtimeIndicesTable(subQueryConfigAdaptYoy, req, infos)
      }
      const arrWithCompareSpec = resolveCompareSpec(subQueryConfig, resultSet, needCalcCompareSpecs)
      return [
        arrWithCompareSpec,
        ...await patchPlanData(arrWithCompareSpec, subQueryConfig, infos, req.totalMutRequester)
      ]
    }
    if (sqlModelId === 'mindex') {
      // mindex 指标
      const res = await queryIndicatorData(subQueryConfig, requester)
      if (!_.isEmpty(res)) {
        return [res]
      }
      // 查指标时，先尝试一起查，如果查不到数据，再尝试分开查
      const metricColsDict = _.pickBy(subQueryConfig.fieldsBinding, c => isAggCol(c))
      const dimColsDict = _.pickBy(subQueryConfig.fieldsBinding, c => !isAggCol(c))
      const querys = _.map(metricColsDict, (c, k) => ({
        ...subQueryConfig,
        fieldsBinding: { ...dimColsDict, [k]: c }
      }))
      return mapAwaitAll(querys, q => queryIndicatorData(q, requester))
    }
    // 数仓指标
    if (!infos?.[0]) {
      return [[{ __msg: `获取数仓指标信息失败: ${subQueryConfig.fieldsBinding[0].id}`, __code: 404, __data: [] }]]
    }
    const { databaseId } = otherIndiceInfos[0]
    if (!subQueryConfig.dbType) {
      const conn = await getConnByDbId(requester, databaseId)
      subQueryConfig.dbType = _.toLower(conn.type || 'MYSQL') as any
      subQueryConfig.rawDbType = _.toLower(conn?.showType || 'MYSQL') as any
    }
    const resultSet = await querySqlMappingModel(subQueryConfig, requester, infos)
    return [
      resultSet,
      ...await patchPlanData(resultSet, subQueryConfig, infos, req.totalMutRequester)
    ]
  }
  // doQuery 会返回多个数组，需要进行 fullJoin（有时需要单独查本期值和目标值或多个本期值单独查）
  const qRes = await flatMapAwaitAll(queryGroup, (v, k) => doQuery(v, k as string))
  const validColls = _.filter(qRes, coll => !_.isEmpty(coll))

  if (_.isEmpty(validColls)) return []
  if (validColls.length === 1) {
    return validColls[0]
  }

  const joinKeys = _.filter(queryConfig.fieldsBinding, c => !isAggCol(c)).map(c => [c.name, c.name])

  const resultSets = queryConfig.queryTotal ? _.map(validColls, arr => arr[0]?.resultSet) : validColls
  // 使用 arquero join 拼接数据，如果是 select 模式，union 结果
  const resData =
    queryConfig.queryMode === 'select'
      ? _.flatten(resultSets)
      : resultSets
        .reduce((acc, d) => {
          if (_.isEmpty(acc)) return from(d)
          try {
            return acc.join_full(from(d), joinKeys)
          } catch (e) {
            return acc
          }
        }, [])
        .objects()
        // 新增：动态处理列名冲突
        .map(obj => {
          const newObj = { ...obj }
          // 动态检测所有可能的列名后缀（如 _1, _2, _3 等）
          const fieldKeys = Object.keys(obj).filter(key => /_\d+$/.test(key))
          const baseKeys = _.uniq(fieldKeys.map(key => key.replace(/_\d+$/, '')))
          // 遍历所有需要处理的连接键字段
          baseKeys.forEach(field => {
            // 动态检测所有可能的列名后缀（如 _1, _2, _3 等）
            const keys = Object.keys(obj).filter(key => key.startsWith(`${field}_`))
            // 合并所有后缀列的值（优先取靠前的表）
            newObj[field] = keys.reduce((acc, key) => acc ?? obj[key], null) ?? obj[field]
            // 删除临时列
            keys.forEach(key => delete newObj[key])
          })
          return newObj
        })

  // join 完后需要重新排序
  const sorted = _.isEmpty(queryConfig.orderBy)
    ? resData
    : _.orderBy(
      resData,
      queryConfig.orderBy.map(o => {
        const { field, col } = o
        return col || queryConfig.fieldsBinding[field]?.name || field
      }),
      queryConfig.orderBy.map(o => o.dir)
    )

  const data = !queryConfig.queryTotal
    ? sorted
    : [_.assign({}, ..._.map(validColls, arr => _.omit(arr[0], 'resultSet')), { resultSet: sorted })]

  return data
}
